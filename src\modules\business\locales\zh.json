{"business": {"title": "企业管理", "description": "管理企业活动", "common": {"save": "保存", "cancel": "取消", "delete": "删除", "edit": "编辑", "create": "创建", "back": "返回", "next": "下一步", "submit": "提交", "search": "搜索", "filter": "筛选", "sort": "排序", "add": "添加", "remove": "移除", "upload": "上传", "download": "下载", "view": "查看", "details": "详情", "actions": "操作", "status": {"active": "活跃", "inactive": "不活跃", "pending": "待处理"}, "all": "全部"}, "customer": {"title": "客户", "description": "管理客户信息", "add": "添加客户", "edit": "编辑客户", "view": "查看客户详情", "addForm": "添加新客户", "editForm": "编辑客户信息", "detailForm": "客户详情", "totalCustomers": "客户总数", "manage": "管理客户", "platform": "平台", "timezone": "时区", "form": {"name": "姓名", "namePlaceholder": "请输入客户姓名", "email": "邮箱", "emailPlaceholder": "请输入邮箱地址", "phone": "电话号码", "phonePlaceholder": "请输入电话号码", "tags": "客户标签", "tagsPlaceholder": "输入标签并按回车", "address": "地址", "addressPlaceholder": "请输入地址"}, "status": {"active": "活跃", "inactive": "非活跃", "blocked": "已屏蔽"}, "detail": {"generalInfo": "基本信息", "social": "社交", "customFields": "自定义字段", "orders": "订单", "activities": "活动", "overview": "概览", "totalOrders": "总订单数", "totalSpent": "总消费", "averageOrderValue": "平均订单价值", "lastOrderDate": "最后订单", "customerSince": "客户起始时间", "interactionChannels": "互动渠道", "orderHistory": "订单历史", "activityLog": "活动日志", "socialProfiles": "社交档案", "customFieldValues": "自定义字段值", "noData": "暂无数据", "noOrders": "暂无订单", "noActivities": "暂无活动", "noInteractions": "暂无互动", "revenue": "收入", "interactions": "互动次数", "topChannels": "热门消息渠道", "topDevices": "热门客户设备", "interactedFlows": "已互动流程", "interactedCampaigns": "已互动活动", "orderList": "订单历史", "orderCode": "订单号", "orderDate": "订单日期", "paymentMethod": "支付方式", "deliveryStatus": "配送状态", "shippingStatus": "配送状态", "paymentStatus": "支付状态", "orderStatus": "订单状态", "source": "来源", "totalAmount": "总金额", "flowName": "流程名称", "lastInteraction": "最后互动", "campaignName": "活动名称", "interactionType": "互动类型", "sent": "已发送", "opened": "已打开", "clicked": "已点击", "noOrdersDesc": "该客户尚未下过任何订单", "noActivitiesDesc": "该客户尚无记录的活动", "allActivities": "所有活动"}, "activity": {"type": "活动类型", "date": "日期", "details": "详情", "moreDetails": "更多详情", "types": {"order": "订单", "login": "登录", "support": "支持", "review": "评价"}}, "overview": {"flowCount": "流程数", "campaignCount": "活动数", "sequenceCount": "序列数", "interactions": "互动次数", "revenue": "收入"}, "social": {"platform": "平台", "username": "用户名", "link": "链接", "editDescription": "输入 {{platform}} 上的用户名或链接", "save": "保存"}, "messages": {"createSuccess": "客户创建成功", "createError": "创建客户时出错", "updateSuccess": "客户更新成功", "updateError": "更新客户时出错", "deleteSuccess": "客户删除成功", "deleteError": "删除客户时出错", "bulkDeleteSuccess": "成功删除{{count}}个客户", "bulkDeleteError": "删除客户时出错"}, "bulkDeleteConfirmation": "您确定要删除{{count}}个选中的客户吗？", "import": {"title": "导入客户"}}, "order": {"notesPlaceholder": "输入订单备注", "title": "订单", "description": "管理订单", "createOrder": "创建新订单", "editOrder": "编辑订单", "viewOrder": "查看订单详情", "orderNumber": "订单号", "customerInfo": "客户信息", "customerName": "客户姓名", "customerEmail": "电子邮件", "customerPhone": "电话", "customerAddress": "地址", "items": "订单商品", "noItems": "此订单没有商品", "quantity": "数量", "totalAmount": "总金额", "status": {"title": "状态", "pending": "待处理", "processing": "处理中", "completed": "已完成", "cancelled": "已取消", "refunded": "已退款"}, "paymentMethod": "支付方式", "paymentMethods": {"cash": "现金", "creditCard": "信用卡", "bankTransfer": "银行转账", "digitalWallet": "电子钱包"}, "paymentStatus": {"title": "支付状态", "paid": "已支付", "unpaid": "未支付", "partiallyPaid": "部分支付"}, "notes": "备注", "shippingMethod": "配送方式", "shippingFee": "配送费用", "shippingNote": "配送备注", "shippingNotePlaceholder": "输入配送备注...", "codAmount": "货到付款金额", "tags": "订单标签", "tagsPlaceholder": "输入标签并按回车", "addTag": "添加标签", "removeTag": "删除标签", "subtotal": "小计", "form": {"customerNamePlaceholder": "输入客户姓名", "customerEmailPlaceholder": "输入客户电子邮件", "customerPhonePlaceholder": "输入客户电话", "customerAddressPlaceholder": "输入客户地址", "notesPlaceholder": "输入订单备注"}, "createSuccess": "订单创建成功", "createError": "创建订单时出错", "updateSuccess": "订单更新成功", "updateError": "更新订单时出错", "deleteSuccess": "订单删除成功", "deleteError": "删除订单时出错", "confirmDeleteMessage": "您确定要删除此订单吗？", "cancel": "取消订单"}, "product": {"title": "产品", "createProduct": "创建产品", "editProduct": "编辑产品", "productList": "产品列表", "productInfo": "产品信息", "productAttributes": "产品属性", "productImages": "产品图片", "name": "产品名称", "tags": "标签", "priceType": {"title": "价格类型", "hasPrice": "固定价格", "stringPrice": "描述性价格", "noPrice": "无价格"}, "customFields": {"title": "自定义字段", "selectField": "选择自定义字段", "selectGroupForm": "选择自定义字段组", "searchPlaceholder": "搜索自定义字段...", "searchGroupPlaceholder": "搜索自定义字段组...", "selectedFields": "已选自定义字段", "selectedGroupForm": "已选自定义字段组", "addField": "添加自定义字段", "addGroupForm": "添加自定义字段组"}, "listPrice": "标价", "salePrice": "销售价", "currency": "货币", "priceDescription": "价格描述", "createSuccess": "产品创建成功", "createError": "创建产品时出错", "updateSuccess": "产品更新成功", "updateError": "更新产品时出错", "deleteSuccess": "产品删除成功", "deleteError": "删除产品时出错", "bulkDeleteSuccess": "成功删除 {{count}} 个产品", "bulkDeleteError": "批量删除产品时出错", "selectToDelete": "请至少选择一个产品进行删除", "confirmDeleteMessage": "您确定要删除此产品吗？", "confirmBulkDeleteMessage": "您确定要删除选定的 {{count}} 个产品吗？", "fields": {"name": "产品名称", "price": "价格", "priceType": "价格类型", "priceTypes": {"yes": "是", "no": "否", "other": "其他"}}, "form": {"title": "添加新产品", "name": "产品名称", "description": "描述", "price": "价格", "category": "类别", "sku": "SKU", "status": "状态", "inventory": "库存", "submit": "保存产品", "cancel": "取消", "createTitle": "添加新产品", "editTitle": "编辑产品", "namePlaceholder": "输入产品名称", "descriptionPlaceholder": "输入产品描述", "pricePlaceholder": "输入产品价格", "categoryPlaceholder": "选择产品类别", "skuPlaceholder": "输入产品SKU", "statusPlaceholder": "选择产品状态", "inventoryPlaceholder": "输入产品库存", "tagsPlaceholder": "输入产品标签", "mediaPlaceholder": "拖放或点击上传产品图片", "media": "产品图片", "shipmentConfig": {"title": "运输配置", "widthCm": "宽度 (cm)", "heightCm": "高度 (cm)", "lengthCm": "长度 (cm)"}, "customFields": {"title": "自定义字段", "selectField": "选择自定义字段", "selectGroupForm": "选择自定义字段组", "searchPlaceholder": "搜索自定义字段...", "searchGroupPlaceholder": "搜索自定义字段组...", "selectedFields": "已选自定义字段", "selectedGroupForm": "已选自定义字段组", "addField": "添加自定义字段", "addGroupForm": "添加自定义字段组"}, "variants": {"title": "产品分类", "addVariant": "添加分类", "variant": "分类", "noVariants": "暂无分类。点击\"添加分类\"开始。", "customFields": "分类属性", "searchCustomField": "搜索属性"}, "priceDescriptionPlaceholder": "输入价格描述", "priceTypePlaceholder": "选择价格类型", "priceTypes": {"yes": "是", "no": "否", "other": "其他"}}, "productDetails": {"regularPrice": "常规价格", "salePrice": "促销价格", "priceNote": "价格备注", "brand": "品牌", "url": "网址", "description": "描述", "attributes": "属性", "attributeName": "属性名称", "attributeType": "数据类型", "attributeValue": "默认值"}, "validation": {"nameRequired": "请输入产品名称", "attributeNameRequired": "请输入属性名称", "attributeTypeRequired": "请选择数据类型"}, "attributeTypes": {"text": "文本", "number": "数字", "date": "日期", "boolean": "是/否", "list": "列表"}, "images": {"addImages": "添加产品图片", "image": "图片", "url": "网址", "video": "视频", "uploadImage": "上传图片", "enterImageUrl": "输入图片网址", "enterVideoUrl": "输入视频网址", "recommendedSize": "建议尺寸：800x600像素，最大2MB", "addToList": "添加到列表", "uploadedImages": "已上传图片", "urlImages": "网址图片", "videoList": "视频列表", "setCover": "设为封面", "coverImage": "封面图片", "uploadedFromComputer": "从电脑上传", "dragAndDrop": "拖放或点击上传产品图片"}, "actions": {"createProduct": "创建产品", "saveProduct": "保存产品", "deleteProduct": "删除产品", "cancelCreation": "取消"}, "messages": {"productCreated": "产品创建成功", "productUpdated": "产品更新成功", "productDeleted": "产品删除成功", "confirmDelete": "您确定要删除此产品吗？"}, "import": {"title": "导入产品", "steps": {"upload": "上传文件", "mapping": "列映射", "preview": "预览数据", "importing": "导入中"}, "mapping": {"title": "列映射", "description": "将Excel列映射到产品字段", "columnMapping": "列映射", "skipColumn": "跳过此列", "requiredField": "此字段是必需的", "dataPreview": "数据预览", "validationErrors": "验证错误", "errors": {"duplicateMapping": "此字段已映射到另一列", "requiredFieldMissing": "必需字段{{field}}未映射"}}, "upload": {"title": "上传产品文件", "description": "选择Excel文件或输入URL来导入产品列表", "fromFile": "从文件", "fromUrl": "从URL", "supportedFormats": "支持：.xlsx, .xls, .csv（最大10MB）", "hasHeader": "文件有标题行", "excelUrl": "Excel文件URL", "urlPlaceholder": "输入Excel文件URL...", "loading": "加载中...", "loadFromUrl": "从URL加载"}, "errors": {"parseError": "解析文件错误", "urlRequired": "URL是必需的", "urlFetchError": "从URL获取文件错误", "urlLoadError": "从URL加载文件错误"}, "progress": {"importing": "正在导入产品", "pleaseWait": "请等待系统处理数据", "processing": "处理中", "imported": "已导入", "errors": "错误"}, "complete": {"title": "导入完成", "description": "产品导入已成功完成", "totalProcessed": "总处理数", "successfullyImported": "成功导入", "failed": "失败", "errorDetails": "错误详情", "nextSteps": "下一步", "reviewProducts": "查看产品列表", "updateInventory": "更新库存水平", "setupCategories": "设置产品类别", "viewProducts": "查看产品"}, "preview": {"title": "预览导入数据", "description": "导入前查看和验证数据", "totalRows": "总行数", "validRows": "有效行", "invalidRows": "无效行", "validationWarnings": "验证警告", "dataPreview": "数据预览", "showingFirst10": "显示前10行", "importOptions": "导入选项", "skipInvalidRows": "跳过无效行", "updateExisting": "更新现有产品", "sendNotification": "发送通知", "startImport": "开始导入", "row": "行"}, "validation": {"nameRequired": "产品名称是必需的", "skuRequired": "SKU是必需的", "priceRequired": "价格是必需的", "invalidPrice": "价格必须是正数", "invalidStock": "库存必须是非负数"}}}, "customField": {"title": "自定义字段", "description": "管理自定义字段", "component": "组件类型", "components": {"input": "输入框", "textarea": "文本区域", "select": "下拉选择", "checkbox": "复选框", "radio": "单选按钮", "date": "日期", "number": "数字", "file": "文件", "multiSelect": "多选"}, "type": "数据类型", "types": {"text": "文本", "number": "数字", "boolean": "是/否", "date": "日期", "select": "选择框", "object": "对象", "array": "数组"}, "name": "字段名称", "label": "标签", "placeholder": "占位符", "defaultValue": "默认值", "options": "选项", "required": "必填", "validation": {"minLength": "最小长度", "maxLength": "最大长度", "pattern": "模式", "min": "最小值", "max": "最大值"}, "booleanValues": {"true": "是", "false": "否"}, "patterns": {"email": "邮箱", "phoneVN": "越南电话号码", "phoneIntl": "国际电话号码", "postalCodeVN": "越南邮政编码", "lettersOnly": "仅字母", "numbersOnly": "仅数字", "alphanumeric": "字母和数字", "noSpecialChars": "无特殊字符", "url": "网址", "ipv4": "IPv4地址", "strongPassword": "强密码", "vietnameseName": "越南姓名", "studentId": "学生证号", "nationalId": "身份证号", "taxCode": "税号", "dateFormat": "日期 (dd/mm/yyyy)", "timeFormat": "时间 (hh:mm)", "hexColor": "十六进制颜色", "base64": "Base64编码", "uuid": "UUID", "filename": "文件名", "urlSlug": "URL别名", "variableName": "变量名", "creditCard": "信用卡号", "qrCode": "二维码", "gpsCoordinate": "GPS坐标", "rgbColor": "RGB颜色", "domain": "域名", "decimal": "小数", "barcode": "条形码"}, "confirmDeleteMessage": "您确定要删除此自定义字段吗？", "createSuccess": "自定义字段创建成功", "createError": "创建自定义字段时出错", "updateSuccess": "自定义字段更新成功", "updateError": "更新自定义字段时出错", "deleteSuccess": "自定义字段删除成功", "deleteError": "删除自定义字段时出错", "loadError": "加载自定义字段时出错", "form": {"componentRequired": "请选择组件类型", "labelRequired": "请输入标签", "typeRequired": "请选择数据类型", "idRequired": "请输入字段标识符名称", "labelPlaceholder": "输入显示标签", "descriptionPlaceholder": "输入描述", "description": "描述", "labelTagRequired": "请至少添加一个标签", "fieldIdLabel": "字段标识符名称", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "显示名称", "displayNamePlaceholder": "输入此字段的显示名称", "displayNameRequired": "请输入显示名称", "labelInputPlaceholder": "输入标签并按回车", "tagsCount": "个标签已添加", "patternSuggestions": "常用模式建议：", "defaultValue": "默认值", "minLength": "最小长度", "maxLength": "最大长度", "pattern": "模式", "options": "选项", "min": "最小值", "max": "最大值", "placeholder": "占位符", "required": "必填", "label": "标签", "optionsPlaceholder": "输入选项，逗号分隔或JSON格式", "selectOptionsPlaceholder": "按Name|Value格式输入值，每行一对。示例：\na|1\nb|2", "booleanDefaultPlaceholder": "选择默认值", "dateDefaultPlaceholder": "选择默认日期", "placeholderPlaceholder": "输入占位符文本", "defaultValuePlaceholder": "输入默认值"}}, "customGroupForm": {"title": "自定义字段组", "description": "管理自定义字段组", "createSuccess": "自定义字段组创建成功", "createError": "创建自定义字段组时出错", "updateSuccess": "自定义字段组更新成功", "updateError": "更新自定义字段组时出错", "deleteSuccess": "自定义字段组删除成功", "deleteError": "删除自定义字段组时出错", "loadError": "加载自定义字段组时出错"}, "warehouse": {"title": "仓库", "description": "管理仓库", "name": "仓库名称", "code": "仓库代码", "desc": "描述", "type": "仓库类型", "types": {"PHYSICAL": "实体仓库", "VIRTUAL": "虚拟仓库"}, "status": "状态", "address": "地址", "contact": "联系信息", "add": "添加仓库", "edit": "编辑仓库", "addForm": "添加新仓库", "editForm": "编辑仓库信息", "createSuccess": "仓库创建成功", "updateSuccess": "仓库更新成功", "deleteSuccess": "仓库删除成功", "createError": "创建仓库时出错", "updateError": "更新仓库时出错", "deleteError": "删除仓库时出错", "confirmDeleteMessage": "您确定要删除此仓库吗？", "form": {"namePlaceholder": "输入仓库名称", "descriptionPlaceholder": "输入仓库描述", "typePlaceholder": "选择仓库类型", "selectType": "选择仓库类型"}}, "physicalWarehouse": {"title": "实体仓库", "description": "管理实体仓库", "manage": "管理实体仓库", "totalWarehouses": "实体仓库总数", "name": "实体仓库名称", "warehouse": "仓库", "address": "地址", "capacity": "容量", "actions": "操作", "add": "添加实体仓库", "create": "创建实体仓库", "edit": "编辑实体仓库", "delete": "删除实体仓库", "view": "查看详情", "search": "搜索实体仓库...", "noData": "无实体仓库数据", "createSuccess": "实体仓库创建成功", "updateSuccess": "实体仓库更新成功", "deleteSuccess": "实体仓库删除成功", "deleteMultipleSuccess": "成功删除多个实体仓库", "createError": "创建实体仓库失败", "updateError": "更新实体仓库失败", "deleteError": "删除实体仓库失败", "deleteMultipleError": "删除多个实体仓库失败", "confirmDeleteMessage": "您确定要删除此实体仓库吗？", "confirmBulkDeleteMessage": "您确定要删除选定的 {{count}} 个实体仓库吗？", "form": {"createTitle": "创建新实体仓库", "editTitle": "编辑实体仓库", "create": "创建实体仓库", "update": "更新", "selectWarehouse": "选择仓库", "warehousePlaceholder": "选择仓库以创建实体仓库", "warehouseRequired": "仓库是必需的", "addressPlaceholder": "输入实体仓库地址", "addressRequired": "地址是必需的", "addressMaxLength": "地址不能超过255个字符", "capacityPlaceholder": "输入仓库容量", "capacityMin": "容量必须大于或等于0"}}, "virtualWarehouse": {"title": "虚拟仓库", "description": "管理虚拟仓库和数字存储系统", "manage": "管理虚拟仓库", "totalWarehouses": "虚拟仓库总数", "name": "虚拟仓库名称", "status": {"title": "状态", "active": "活跃", "inactive": "非活跃"}, "associatedSystem": "关联系统", "purpose": "用途", "actions": "操作", "create": "创建虚拟仓库", "edit": "编辑虚拟仓库", "delete": "删除虚拟仓库", "view": "查看详情", "search": "搜索虚拟仓库...", "noData": "无虚拟仓库数据", "createSuccess": "虚拟仓库创建成功", "updateSuccess": "虚拟仓库更新成功", "deleteSuccess": "虚拟仓库删除成功", "deleteMultipleSuccess": "成功删除多个虚拟仓库", "createError": "创建虚拟仓库失败", "updateError": "更新虚拟仓库失败", "deleteError": "删除虚拟仓库失败", "deleteMultipleError": "删除多个虚拟仓库失败", "confirmDeleteMessage": "您确定要删除此虚拟仓库吗？", "confirmBulkDeleteMessage": "您确定要删除选定的 {{count}} 个虚拟仓库吗？", "form": {"createTitle": "创建新虚拟仓库", "editTitle": "编辑虚拟仓库", "create": "创建虚拟仓库", "update": "更新", "warehousePlaceholder": "选择仓库以创建虚拟仓库", "warehouseRequired": "仓库是必需的", "descriptionPlaceholder": "输入虚拟仓库描述", "descriptionMaxLength": "描述不能超过500个字符", "associatedSystemPlaceholder": "输入关联系统", "associatedSystemMaxLength": "关联系统不能超过200个字符", "purposePlaceholder": "输入用途", "purposeMaxLength": "用途不能超过300个字符", "statusPlaceholder": "选择状态", "statusRequired": "状态是必需的"}}, "inventory": {"title": "库存管理", "description": "管理库存和出入库", "totalItems": "总商品数", "manage": "管理库存", "status": {"inStock": "有库存", "lowStock": "库存不足", "outOfStock": "无库存"}}, "conversion": {"title": "转化", "description": "跟踪和管理转化", "totalConversions": "总转化数", "manage": "管理转化", "id": "ID", "customerId": "客户ID", "userId": "用户ID", "type": "转化类型", "name": "名称", "source": "来源", "destination": "目的地", "value": "价值", "date": "日期", "status": {"completed": "已完成", "pending": "处理中", "failed": "失败"}}}}